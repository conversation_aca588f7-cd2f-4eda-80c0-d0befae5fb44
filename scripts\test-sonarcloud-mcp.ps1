# Test SonarCloud MCP Server Integration
# This script tests the SonarCloud MCP server installation and basic functionality

param(
    [Parameter(Mandatory=$true)]
    [string]$SonarCloudToken,
    
    [string]$Organization = "alexandru-savinov",
    [string]$ProjectKey = "alexandru-savinov_BalancedNewsGo"
)

Write-Host "Testing SonarCloud MCP Server Integration" -ForegroundColor Blue
Write-Host "=========================================" -ForegroundColor Blue
Write-Host ""

# Set environment variables
$env:SONARQUBE_URL = "https://sonarcloud.io"
$env:SONARQUBE_TOKEN = $SonarCloudToken
$env:SONARQUBE_ORGANIZATION = $Organization

Write-Host "1. Checking npm installation..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "✅ npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not found. Please install Node.js and npm." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. Checking SonarQube MCP Server installation..." -ForegroundColor Yellow
try {
    $packageInfo = npm list -g sonarqube-mcp-server 2>$null
    if ($packageInfo -match "sonarqube-mcp-server@") {
        Write-Host "✅ SonarQube MCP Server is installed" -ForegroundColor Green
        Write-Host $packageInfo -ForegroundColor Gray
    } else {
        Write-Host "❌ SonarQube MCP Server not found. Installing..." -ForegroundColor Yellow
        npm install -g sonarqube-mcp-server@latest
        Write-Host "✅ SonarQube MCP Server installed" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Error checking package installation: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "3. Testing SonarCloud API connectivity..." -ForegroundColor Yellow

# Test SonarCloud API directly
$headers = @{
    'Authorization' = "Bearer $SonarCloudToken"
}

try {
    $response = Invoke-RestMethod -Uri "https://sonarcloud.io/api/projects/search?organization=$Organization" -Headers $headers -Method Get
    Write-Host "✅ SonarCloud API connection successful" -ForegroundColor Green
    
    $project = $response.components | Where-Object { $_.key -eq $ProjectKey }
    if ($project) {
        Write-Host "✅ Project '$ProjectKey' found" -ForegroundColor Green
        Write-Host "   Name: $($project.name)" -ForegroundColor Gray
        Write-Host "   Last Analysis: $($project.lastAnalysisDate)" -ForegroundColor Gray
    } else {
        Write-Host "⚠️  Project '$ProjectKey' not found in organization '$Organization'" -ForegroundColor Yellow
        Write-Host "Available projects:" -ForegroundColor Gray
        $response.components | ForEach-Object { Write-Host "   - $($_.key): $($_.name)" -ForegroundColor Gray }
    }
} catch {
    Write-Host "❌ SonarCloud API connection failed: $_" -ForegroundColor Red
    Write-Host "Please check your token and organization settings." -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "4. Creating Claude Desktop configuration..." -ForegroundColor Yellow

$claudeConfigPath = "$env:APPDATA\Claude\claude_desktop_config.json"
$claudeConfigDir = Split-Path $claudeConfigPath -Parent

# Create Claude directory if it doesn't exist
if (!(Test-Path $claudeConfigDir)) {
    New-Item -ItemType Directory -Path $claudeConfigDir -Force | Out-Null
    Write-Host "✅ Created Claude configuration directory" -ForegroundColor Green
}

# Create or update Claude Desktop configuration
$claudeConfig = @{
    mcpServers = @{
        sonarqube = @{
            command = "npx"
            args = @("-y", "sonarqube-mcp-server@latest")
            env = @{
                SONARQUBE_URL = "https://sonarcloud.io"
                SONARQUBE_TOKEN = $SonarCloudToken
                SONARQUBE_ORGANIZATION = $Organization
            }
        }
    }
}

try {
    $claudeConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $claudeConfigPath -Encoding UTF8
    Write-Host "✅ Claude Desktop configuration created at: $claudeConfigPath" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create Claude Desktop configuration: $_" -ForegroundColor Red
    Write-Host "Manual configuration required." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "5. Testing MCP server startup..." -ForegroundColor Yellow

# Test if the MCP server can start (this will timeout quickly as it waits for stdin)
try {
    $process = Start-Process -FilePath "npx" -ArgumentList "-y", "sonarqube-mcp-server@latest" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 3
    
    if (!$process.HasExited) {
        Write-Host "✅ MCP server started successfully" -ForegroundColor Green
        $process.Kill()
    } else {
        Write-Host "⚠️  MCP server exited immediately (this may be normal)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not test MCP server startup: $_" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Setup Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart Claude Desktop to load the MCP server" -ForegroundColor White
Write-Host "2. Test the integration by asking Claude: 'List my SonarQube projects'" -ForegroundColor White
Write-Host "3. Try: 'Show me the quality gate status for BalancedNewsGo'" -ForegroundColor White
Write-Host "4. Explore: 'Find critical issues in the main branch'" -ForegroundColor White
Write-Host ""
Write-Host "For troubleshooting, see: docs/SonarCloud-MCP-Setup.md" -ForegroundColor Gray
