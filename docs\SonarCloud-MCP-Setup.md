# SonarCloud MCP Server Setup for BalancedNewsGo

## Overview

This document describes the installation and configuration of the SonarCloud MCP (Model Context Protocol) server for the BalancedNewsGo project. This integration allows AI assistants like <PERSON> to interact directly with your SonarCloud project for code quality analysis.

## Research Results

After researching available SonarCloud MCP servers, I identified two main options:

1. **sapientpants/sonarqube-mcp-server** - 64 stars ⭐ (Community-maintained, TypeScript)
2. **SonarSource/sonarqube-mcp-server** - 40 stars (Official from SonarSource, Java)

**Selected**: `sapientpants/sonarqube-mcp-server` for its popularity, comprehensive features, and excellent documentation.

## Installation

### Prerequisites

- Node.js 20 or higher
- npm or npx
- SonarCloud account with access to the `alexandru-savinov/BalancedNewsGo` project
- SonarCloud authentication token

### Step 1: Install the MCP Server

```bash
npm install -g sonarqube-mcp-server@latest
```

### Step 2: Get Your SonarCloud Token

1. Log in to [SonarCloud](https://sonarcloud.io)
2. Go to **My Account** → **Security**
3. Generate a new token with appropriate permissions
4. Save the token securely

### Step 3: Configure Claude Desktop

Create or edit your Claude Desktop configuration file:

**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Linux**: `~/.config/Claude/claude_desktop_config.json`

Add the following configuration:

```json
{
  "mcpServers": {
    "sonarqube": {
      "command": "npx",
      "args": ["-y", "sonarqube-mcp-server@latest"],
      "env": {
        "SONARQUBE_URL": "https://sonarcloud.io",
        "SONARQUBE_TOKEN": "YOUR_SONARCLOUD_TOKEN_HERE",
        "SONARQUBE_ORGANIZATION": "alexandru-savinov"
      }
    }
  }
}
```

### Step 4: Restart Claude Desktop

After saving the configuration, restart Claude Desktop to load the MCP server.

## Project Configuration

Your project is already configured for SonarCloud with the following settings:

- **Project Key**: `alexandru-savinov_BalancedNewsGo`
- **Organization**: `alexandru-savinov`
- **URL**: `https://sonarcloud.io`

## Available Operations

Once configured, you can use the following operations through Claude:

### 1. Project Management
- List all SonarQube projects
- Get project details and metadata

### 2. Code Quality Analysis
- Retrieve quality gate status
- Get code coverage metrics
- Analyze code quality trends

### 3. Issue Management
- Search and filter issues by severity, type, status
- Get detailed issue information
- Mark issues as false positive or won't fix
- Add comments to issues
- Assign issues to team members

### 4. Security Analysis
- Find security hotspots
- Review security vulnerabilities
- Get security compliance reports

### 5. Metrics and Measures
- Get component measures (lines of code, complexity, etc.)
- Retrieve historical metrics data
- Compare metrics across branches

### 6. Source Code Analysis
- View source code with issues highlighted
- Get SCM blame information
- Analyze specific files or directories

## Example Usage

Here are some example commands you can use with Claude:

```
"Show me the quality gate status for BalancedNewsGo"
"List all critical issues in the main branch"
"Get code coverage metrics for the project"
"Find security hotspots that need review"
"Show me issues created in the last week"
"Analyze code quality trends over the last month"
"Get details for issue ABC-123"
"Mark issue XYZ-456 as false positive"
```

## Testing the Integration

To verify the integration works:

1. Open Claude Desktop
2. Start a new conversation
3. Ask: "List my SonarQube projects"
4. You should see your BalancedNewsGo project in the response

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify your SonarCloud token is valid
   - Check token permissions include project access

2. **Project Not Found**
   - Confirm the organization key is correct: `alexandru-savinov`
   - Verify you have access to the project

3. **MCP Server Not Loading**
   - Check Claude Desktop configuration syntax
   - Restart Claude Desktop after configuration changes
   - Verify npm package is installed correctly

### Debug Mode

Enable debug logging by adding to the environment variables:
```json
"LOG_LEVEL": "DEBUG",
"LOG_FILE": "/tmp/sonarqube-mcp.log"
```

## Security Considerations

- Store your SonarCloud token securely
- Use tokens with minimal required permissions
- Regularly rotate authentication tokens
- Don't commit tokens to version control

## Alternative: Official SonarSource MCP Server

If you prefer the official server from SonarSource:

```json
{
  "mcpServers": {
    "sonarqube": {
      "command": "docker",
      "args": [
        "run", "-i", "--rm",
        "-e", "SONARQUBE_TOKEN",
        "-e", "SONARQUBE_ORG",
        "mcp/sonarqube"
      ],
      "env": {
        "SONARQUBE_TOKEN": "YOUR_TOKEN",
        "SONARQUBE_ORG": "alexandru-savinov"
      }
    }
  }
}
```

## Next Steps

1. Install and configure the MCP server
2. Test basic operations
3. Integrate into your development workflow
4. Set up automated quality monitoring
5. Train team members on available commands

## Support

- [SonarQube MCP Server Documentation](https://github.com/sapientpants/sonarqube-mcp-server)
- [SonarCloud Documentation](https://docs.sonarcloud.io/)
- [Model Context Protocol Specification](https://modelcontextprotocol.io/)
